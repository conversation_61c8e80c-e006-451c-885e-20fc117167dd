using Godot;

/// <summary>
/// Enum for different enemy types in the game
/// </summary>
public enum EnemyType
{
    None = 0,
    
    // Melee Enemies (1-19)
    Goblin = 1,
    Orc = 2,
    Skeleton = 3,
    
    // Ranged Enemies (20-39)
    Archer = 20,
    Mage = 21,
    
    // Summoner Enemies (40-59)
    Necromancer = 40,
    <PERSON><PERSON> = 41,
    
    // <PERSON> (60-79)
    GoblinKing = 60,
    DragonLord = 61
}

/// <summary>
/// Enemy state machine states
/// </summary>
public enum EnemyState
{
    Patrolling,     // Random movement within territory
    Pursuing,       // Chasing target
    Attacking,      // In combat range
    Returning,      // Returning to territory
    Stunned,        // Temporary disable after damage
    Dead            // Enemy is dead
}

/// <summary>
/// Enemy behavior types for AI system
/// </summary>
public enum EnemyBehaviorType
{
    Territorial,    // Defensive - patrols territory, pursues when threatened
    Aggressive      // Offensive - roams freely, actively hunts targets
}

/// <summary>
/// Target priority types for enemy AI
/// </summary>
public enum TargetType
{
    None = 0,
    Player = 100,
    CombatBuilding = 80,
    ProductionBuilding = 60,
    Wall = 40
}

/// <summary>
/// Helper methods for EnemyType enum
/// </summary>
public static class EnemyTypeExtensions
{
    /// <summary>
    /// Get the maximum health for this enemy type
    /// </summary>
    public static int GetMaxHealth(this EnemyType enemyType)
    {
        return enemyType switch
        {
            EnemyType.Goblin => 5,
            EnemyType.Orc => 35,
            EnemyType.Skeleton => 15,
            EnemyType.Archer => 20,
            EnemyType.Mage => 18,
            EnemyType.Necromancer => 30,
            EnemyType.Shaman => 28,
            EnemyType.GoblinKing => 100,
            EnemyType.DragonLord => 200,
            _ => 20 // Default health
        };
    }
    
    /// <summary>
    /// Get the attack damage for this enemy type
    /// </summary>
    public static int GetAttackDamage(this EnemyType enemyType)
    {
        return enemyType switch
        {
            EnemyType.Goblin => 1,
            EnemyType.Orc => 12,
            EnemyType.Skeleton => 6,
            EnemyType.Archer => 7,
            EnemyType.Mage => 10,
            EnemyType.Necromancer => 5,
            EnemyType.Shaman => 8,
            EnemyType.GoblinKing => 20,
            EnemyType.DragonLord => 35,
            _ => 5 // Default damage
        };
    }
    
    /// <summary>
    /// Get the movement speed for this enemy type
    /// </summary>
    public static float GetMovementSpeed(this EnemyType enemyType)
    {
        // Enemy speeds are 90% of player speed (100.0f) = 90.0f base
        return enemyType switch
        {
            EnemyType.Goblin => 20.0f,
            EnemyType.Orc => 80.0f,
            EnemyType.Skeleton => 95.0f,
            EnemyType.Archer => 85.0f,
            EnemyType.Mage => 80.0f,
            EnemyType.Necromancer => 75.0f,
            EnemyType.Shaman => 78.0f,
            EnemyType.GoblinKing => 85.0f,
            EnemyType.DragonLord => 95.0f,
            _ => 90.0f // Default speed (90% of player)
        };
    }
    
    /// <summary>
    /// Get the XP reward for defeating this enemy type
    /// </summary>
    public static int GetXpReward(this EnemyType enemyType)
    {
        return enemyType switch
        {
            EnemyType.Goblin => 15,
            EnemyType.Orc => 25,
            EnemyType.Skeleton => 10,
            EnemyType.Archer => 18,
            EnemyType.Mage => 20,
            EnemyType.Necromancer => 35,
            EnemyType.Shaman => 30,
            EnemyType.GoblinKing => 100,
            EnemyType.DragonLord => 250,
            _ => 10 // Default XP
        };
    }
    
    /// <summary>
    /// Get the detection range for this enemy type
    /// </summary>
    public static float GetDetectionRange(this EnemyType enemyType)
    {
        return enemyType switch
        {
            EnemyType.Goblin => 80.0f,
            EnemyType.Orc => 90.0f,
            EnemyType.Skeleton => 70.0f,
            EnemyType.Archer => 120.0f,
            EnemyType.Mage => 100.0f,
            EnemyType.Necromancer => 110.0f,
            EnemyType.Shaman => 95.0f,
            EnemyType.GoblinKing => 150.0f,
            EnemyType.DragonLord => 200.0f,
            _ => 80.0f // Default detection range
        };
    }
    
    /// <summary>
    /// Get the attack range for this enemy type
    /// </summary>
    public static float GetAttackRange(this EnemyType enemyType)
    {
        return enemyType switch
        {
            EnemyType.Goblin => 24.0f,
            EnemyType.Orc => 28.0f,
            EnemyType.Skeleton => 22.0f,
            EnemyType.Archer => 100.0f,
            EnemyType.Mage => 80.0f,
            EnemyType.Necromancer => 60.0f,
            EnemyType.Shaman => 70.0f,
            EnemyType.GoblinKing => 32.0f,
            EnemyType.DragonLord => 50.0f,
            _ => 24.0f // Default attack range
        };
    }
}
