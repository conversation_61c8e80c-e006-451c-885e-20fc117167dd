[gd_scene load_steps=26 format=3 uid="uid://dd328ucgp21ei"]

[ext_resource type="Texture2D" uid="uid://cvvr35i1o46r0" path="res://resources/solaria/SpritePack/old/solaria_character_npc.png" id="1_gnnrd"]
[ext_resource type="Script" uid="uid://ch4ta7gk3yofh" path="res://scenes/npcs/TutorialNPC.cs" id="1_script"]
[ext_resource type="Texture2D" uid="uid://c6pcs5p57mb0m" path="res://resources/solaria/UI/dialogs/dialog1.png" id="2_qhmda"]
[ext_resource type="Texture2D" uid="uid://coohtgqediyob" path="res://resources/solaria/UI/dialogs/dialogGreenArrow.png" id="3_806w0"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="4_806w0"]
[ext_resource type="Texture2D" uid="uid://cdbuvqvfmmev" path="res://resources/ELV_itchio/Light Spells/Lightning_Explosion.png" id="6_gnnrd"]

[sub_resource type="Animation" id="Animation_gnnrd"]
resource_name = "Animate"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3]
}

[sub_resource type="Animation" id="Animation_0vg6t"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_10txw"]
_data = {
&"Animate": SubResource("Animation_gnnrd"),
&"RESET": SubResource("Animation_0vg6t")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_5evfb"]
size = Vector2(8, 4)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_1mxkk"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_gnnrd"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_bj88v"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_papxs"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_0vg6t"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_10txw"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_nhcfw"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_whogm"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_txsbh"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_emg2x"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_vbhon"]

[sub_resource type="CircleShape2D" id="CircleShape2D_806w0"]
radius = 13.0

[sub_resource type="Animation" id="Animation_bj88v"]
resource_name = "HideNpc"
length = 1.4
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.10206, 1.2, 1.3),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Sprite2D:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_papxs"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Sprite2D:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_0vg6t"]
_data = {
&"HideNpc": SubResource("Animation_bj88v"),
&"RESET": SubResource("Animation_papxs")
}

[node name="TutorialNpc" type="Sprite2D"]
y_sort_enabled = true
texture = ExtResource("1_gnnrd")
hframes = 4
script = ExtResource("1_script")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_10txw")
}
speed_scale = 0.6

[node name="StaticBody2d" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2d"]
y_sort_enabled = true
position = Vector2(0, 10)
shape = SubResource("RectangleShape2D_5evfb")

[node name="Dialog" type="Node2D" parent="."]
z_index = 1

[node name="DialogPanel" type="Sprite2D" parent="Dialog"]
position = Vector2(-34, -20)
scale = Vector2(0.5, 0.5)
texture = ExtResource("2_qhmda")

[node name="ContinueMark" type="Sprite2D" parent="Dialog"]
position = Vector2(-34, -13)
scale = Vector2(0.5, 0.5)
texture = ExtResource("3_806w0")

[node name="Label" parent="Dialog" instance=ExtResource("4_806w0")]
offset_left = -74.0
offset_top = -40.0
offset_right = 121.0
offset_bottom = 21.0
scale = Vector2(0.41, 0.41)
text = "Hello friend... this is a test message to see how the text will be distributed. "

[node name="DialogContinueButton" type="Button" parent="Dialog"]
offset_left = -74.0
offset_top = -40.0
offset_right = 6.0
offset_bottom = -8.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_1mxkk")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_gnnrd")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_bj88v")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_papxs")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_0vg6t")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_10txw")
theme_override_styles/hover = SubResource("StyleBoxEmpty_nhcfw")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_whogm")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_txsbh")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_emg2x")
theme_override_styles/normal = SubResource("StyleBoxEmpty_vbhon")

[node name="PlayerDetectionArea2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetectionArea2D"]
position = Vector2(0, 3)
shape = SubResource("CircleShape2D_806w0")

[node name="DisapearAnimation" type="Node2D" parent="."]

[node name="AnimationPlayer" type="AnimationPlayer" parent="DisapearAnimation"]
libraries = {
&"": SubResource("AnimationLibrary_0vg6t")
}

[node name="Sprite2D" type="Sprite2D" parent="DisapearAnimation"]
texture = ExtResource("6_gnnrd")
hframes = 6
vframes = 4
