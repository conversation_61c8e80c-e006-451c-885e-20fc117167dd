using Godot;

public partial class Arrow : Area2D
{
	[Export]
	public float Speed = 200.0f;
	[Export]
	public float MaxDistance = 150.0f;
	
	private Vector2 _direction;
	private Vector2 _startPosition;
	private float _traveledDistance = 0.0f;
	private AnimationPlayer _animationPlayer;
	private Sprite2D _sprite;
	
	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("ArrowAnimation");
		_sprite = GetNode<Sprite2D>("ArrowSprite");
		_startPosition = GlobalPosition;

		BodyEntered += OnBodyEntered;
		AreaEntered += OnAreaEntered;

		_animationPlayer.Play("arrow_fly");
	}
	
	public void Initialize(Vector2 direction)
	{
		_direction = direction.Normalized();
		
		// Rotate arrow to face direction
		float angle = _direction.Angle();
		Rotation = angle + Mathf.Pi / 2; // Add 90 degrees since arrow image points down
	}
	
	public override void _PhysicsProcess(double delta)
	{
		// Move arrow
		Vector2 velocity = _direction * Speed;
		GlobalPosition += velocity * (float)delta;
		
		// Check distance traveled
		_traveledDistance = _startPosition.DistanceTo(GlobalPosition);
		
		if (_traveledDistance >= MaxDistance)
		{
			QueueFree();
		}
	}
	
	private void OnBodyEntered(Node2D body)
	{
		GD.Print($"ARROW HIT BODY {body.Name}");
		// Don't hit the player who shot the arrow
		if (body.Name == "Player")
		{
			return;
		}

		// Check if it's a Region10Boss
		if (body is Region10Boss boss)
		{
			int bowLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.Bow, out int level) ? level : 1;
			int damage = bowLevel + 2;
			boss.TakeDamage(damage);
			GD.Print($"Arrow hit Region10Boss for {damage} damage!");
			QueueFree();
			return;
		}

		// Check if it's a Region10SerpentFly
		if (body is Region10SerpentFly serpent)
		{
			int bowLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.Bow, out int level) ? level : 1;
			int damage = bowLevel + 2;
			serpent.TakeDamage(damage);
			GD.Print($"Arrow hit Region10SerpentFly for {damage} damage!");
			QueueFree();
			return;
		}

		// Check if it's an enemy
		if (body is BaseEnemy enemy)
		{
			// Apply damage to enemy
			enemy.TakeDamage(3); // Arrow damage
			GD.Print($"Arrow hit {enemy.GetEnemyType()}!");
			QueueFree();
			return;
		}

		// Hit something else (wall, building, etc.)
		QueueFree();
	}

	private void OnAreaEntered(Area2D area)
	{
		if (area.Name == "HitArea")
		{
			var rabbit = area.GetParent();
			if (rabbit != null && rabbit.HasMethod("TakeDamage"))
			{
				// No knockback - just damage and flash
				rabbit.Call("ApplyDamageFlash");
				rabbit.Call("TakeDamage", 3);
			}
			QueueFree();
		}
	}
}
