[gd_scene load_steps=12 format=3 uid="uid://i823ss5ka6ek"]

[ext_resource type="Texture2D" uid="uid://x6s2bxgexypg" path="res://resources/solaria/exterior/monument_1.png" id="1_esvnd"]
[ext_resource type="Texture2D" uid="uid://6osirkqsvls6" path="res://resources/solaria/UI/monument/monumentPanel.png" id="2_66dgy"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="3_tjq61"]
[ext_resource type="Texture2D" uid="uid://cdxye6tum1anb" path="res://resources/solaria/UI/inventory/close_button.png" id="4_cgria"]
[ext_resource type="Script" uid="uid://i6fbi3ays8mc" path="res://scenes/regions/region9/Region9Statue.cs" id="7_region9statue_script"]

[sub_resource type="Animation" id="Animation_3wdsc"]
resource_name = "Close"
length = 0.2
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("CanvasLayer/Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.06, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.2, 1.2), Vector2(0.8, 0.8)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CanvasLayer/Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.2),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_jbqqb"]
resource_name = "Open"
length = 0.2
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("CanvasLayer/Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.120378, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.8, 0.8), Vector2(1.2, 1.2), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CanvasLayer/Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_5dvj7"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("CanvasLayer/Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CanvasLayer/Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_ulm1m"]
_data = {
&"Close": SubResource("Animation_3wdsc"),
&"Open": SubResource("Animation_jbqqb"),
&"RESET": SubResource("Animation_5dvj7")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_7p3ne"]
size = Vector2(16, 16)

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_j5ju7"]
height = 22.0

[node name="Region9Statue" type="Node2D"]
y_sort_enabled = true
script = ExtResource("7_region9statue_script")

[node name="Sprite2D" type="Sprite2D" parent="."]
y_sort_enabled = true
texture = ExtResource("1_esvnd")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_ulm1m")
}

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 8)
shape = SubResource("RectangleShape2D_7p3ne")

[node name="PlayerDetector" type="Area2D" parent="."]
collision_mask = 4

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
position = Vector2(0, 7)
shape = SubResource("CapsuleShape2D_j5ju7")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="Control" type="Control" parent="CanvasLayer"]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Node2D" parent="CanvasLayer/Control"]

[node name="Panel" type="Sprite2D" parent="CanvasLayer/Control/Panel"]
texture = ExtResource("2_66dgy")

[node name="Header" parent="CanvasLayer/Control/Panel" instance=ExtResource("3_tjq61")]
offset_left = -62.0
offset_top = -29.0
offset_right = 76.0
offset_bottom = -9.0
scale = Vector2(0.9, 0.9)

[node name="Description" parent="CanvasLayer/Control/Panel" instance=ExtResource("3_tjq61")]
offset_left = -62.0
offset_top = -9.0
offset_right = 145.0
offset_bottom = 53.0
scale = Vector2(0.6, 0.6)
vertical_alignment = 0

[node name="Close" type="Sprite2D" parent="CanvasLayer/Control/Panel"]
position = Vector2(66, -32)
scale = Vector2(0.8, 0.8)
texture = ExtResource("4_cgria")

[node name="CloseButton" type="Button" parent="CanvasLayer/Control/Panel"]
offset_left = 58.0
offset_top = -42.0
offset_right = 74.0
offset_bottom = -25.0
